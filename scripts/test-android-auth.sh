#!/bin/bash

# Android Authentication Test Script
# This script helps diagnose authentication issues on Android devices

echo "🔍 Android Authentication Diagnostic Script"
echo "==========================================="
echo ""

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
    echo "❌ Error: Please run this script from the project root directory"
    exit 1
fi

# Check if Android device is connected
echo "📱 Checking Android device connection..."
adb devices | grep -q "device$"
if [ $? -ne 0 ]; then
    echo "❌ No Android device found. Please connect an Android device and enable USB debugging."
    exit 1
fi

echo "✅ Android device connected"
echo ""

# Get device information
echo "📋 Device Information:"
echo "----------------------"
DEVICE_MODEL=$(adb shell getprop ro.product.model)
ANDROID_VERSION=$(adb shell getprop ro.build.version.release)
API_LEVEL=$(adb shell getprop ro.build.version.sdk)

echo "Device Model: $DEVICE_MODEL"
echo "Android Version: $ANDROID_VERSION"
echo "API Level: $API_LEVEL"
echo ""

# Check if app is installed
echo "📦 Checking app installation..."
APP_PACKAGE="tr.com.digiturk.digihr"
adb shell pm list packages | grep -q "$APP_PACKAGE"
if [ $? -ne 0 ]; then
    echo "❌ DigiHR app is not installed on the device"
    echo "Please install the app first using: npm run android"
    exit 1
fi

echo "✅ DigiHR app is installed"
echo ""

# Check network connectivity
echo "🌐 Testing network connectivity..."
adb shell ping -c 3 digiflowtest.digiturk.com.tr > /dev/null 2>&1
if [ $? -eq 0 ]; then
    echo "✅ Network connectivity to digiflowtest.digiturk.com.tr: OK"
else
    echo "❌ Network connectivity to digiflowtest.digiturk.com.tr: FAILED"
    echo "Please check device internet connection"
fi
echo ""

# Test API endpoints
echo "🔗 Testing API endpoints..."
echo "----------------------------"

# Test mobile API endpoint
MOBILE_API_URL="https://digiflowtest.digiturk.com.tr/mobile"
echo "Testing Mobile API: $MOBILE_API_URL"

# Use curl to test the endpoint
curl -s -o /dev/null -w "HTTP Status: %{http_code}\nResponse Time: %{time_total}s\n" \
     -H "User-Agent: DigiHR/1.0.0 (Android $ANDROID_VERSION; Test)" \
     -H "Content-Type: application/json" \
     -H "X-Mobile-App: true" \
     -H "X-Is-Mobile: true" \
     "$MOBILE_API_URL/auth/login" \
     --connect-timeout 10 \
     --max-time 30

echo ""

# Test auth endpoint specifically
echo "Testing Auth Endpoint Response:"
AUTH_RESPONSE=$(curl -s -X OPTIONS \
     -H "User-Agent: DigiHR/1.0.0 (Android $ANDROID_VERSION; Test)" \
     -H "Content-Type: application/json" \
     -H "X-Mobile-App: true" \
     -H "X-Is-Mobile: true" \
     "$MOBILE_API_URL/auth/login" \
     --connect-timeout 10 \
     --max-time 30)

if echo "$AUTH_RESPONSE" | grep -q "NTLM\|Windows Authentication"; then
    echo "❌ NTLM Authentication detected - this is the main issue!"
    echo "The server is configured with Windows Authentication which conflicts with mobile JWT auth."
    echo ""
    echo "🔧 SOLUTION REQUIRED:"
    echo "Contact the server administrator to disable Windows Authentication for /mobile endpoints"
    echo "Add this to web.config:"
    echo ""
    echo "<location path=\"mobile\">"
    echo "  <system.webServer>"
    echo "    <security>"
    echo "      <authentication>"
    echo "        <windowsAuthentication enabled=\"false\" />"
    echo "        <anonymousAuthentication enabled=\"true\" />"
    echo "      </authentication>"
    echo "    </security>"
    echo "  </system.webServer>"
    echo "</location>"
else
    echo "✅ No NTLM authentication detected"
fi

echo ""

# Check app logs
echo "📋 Recent app logs (last 50 lines):"
echo "------------------------------------"
adb logcat -t 50 | grep -i "digihr\|auth\|login\|error" || echo "No relevant logs found"

echo ""

# Check app storage
echo "💾 Checking app storage..."
echo "--------------------------"
adb shell run-as $APP_PACKAGE ls -la files/ 2>/dev/null || echo "Cannot access app storage (requires root or debuggable app)"

echo ""

# Final recommendations
echo "🎯 Diagnostic Summary and Recommendations:"
echo "==========================================="
echo ""
echo "1. ✅ Device and app setup appears correct"
echo "2. 🔍 Check the API endpoint test results above"
echo "3. 📋 Review the app logs for specific error messages"
echo ""
echo "If NTLM authentication was detected:"
echo "  - This is the primary issue blocking authentication"
echo "  - Server-side configuration change is required"
echo "  - Contact infrastructure/server team immediately"
echo ""
echo "If no NTLM detected but authentication still fails:"
echo "  - Check app logs for specific error messages"
echo "  - Verify credentials are correct"
echo "  - Test with different user accounts"
echo "  - Check device security settings"
echo ""
echo "For further debugging, run the app and check:"
echo "  - Metro bundler logs: npm start"
echo "  - Android logs: adb logcat | grep DigiHR"
echo "  - Network traffic with proxy tools"
echo ""
echo "🔍 Diagnostic complete!"
