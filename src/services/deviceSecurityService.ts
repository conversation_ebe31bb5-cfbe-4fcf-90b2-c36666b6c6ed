import { <PERSON><PERSON>, Platform, BackHandler, NativeModules } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useAuthStore } from '../store/authStore';
import RootJailbreakDetectionService, { DeviceSecurityStatus } from './rootJailbreakDetectionService';

// Dynamically import JailMonkey with error handling
let JailMonkey: any = null;
try {
  JailMonkey = require('jail-monkey').default;
} catch (error) {
  console.warn('JailMonkey module not available in development mode');
}

// Native security module
const { SecurityModule } = NativeModules;

export interface SecurityCheckResult {
  isSecure: boolean;
  issues: SecurityIssue[];
  timestamp: Date;
}

export interface SecurityIssue {
  type: SecurityIssueType;
  severity: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  action: SecurityAction;
}

export enum SecurityIssueType {
  JAILBROKEN = 'JAILBROKEN',
  ROOTED = 'ROOTED',
  DEBUGGER_ATTACHED = 'DEBUGGER_ATTACHED',
  MOCK_LOCATION = 'MOCK_LOCATION',
  EMULATOR = 'EMULATOR',
  TAMPERED_APP = 'TAMPERED_APP',
  HOOK_DETECTED = 'HOOK_DETECTED',
  SUSPICIOUS_FILES = 'SUSPICIOUS_FILES',
  SUSPICIOUS_PACKAGES = 'SUSPICIOUS_PACKAGES',
}

export enum SecurityAction {
  WARN = 'WARN',
  RESTRICT_FEATURES = 'RESTRICT_FEATURES',
  BLOCK_APP = 'BLOCK_APP',
}

interface SecurityEventLog {
  timestamp: Date;
  event: string;
  details: any;
  severity: string;
}

export class DeviceSecurityService {
  private static instance: DeviceSecurityService;
  private checkInterval: NodeJS.Timeout | null = null;
  private readonly SECURITY_CHECK_INTERVAL = 60000; // 1 minute
  private readonly SECURITY_LOGS_KEY = 'security_logs';
  private readonly MAX_LOG_ENTRIES = 100;
  private securityCheckCallbacks: ((result: SecurityCheckResult) => void)[] = [];
  
  private constructor() {
    // Private constructor for singleton
  }
  
  static getInstance(): DeviceSecurityService {
    if (!DeviceSecurityService.instance) {
      DeviceSecurityService.instance = new DeviceSecurityService();
    }
    return DeviceSecurityService.instance;
  }
  
  // Start continuous security monitoring
  startSecurityChecks(): void {
    // Perform initial check
    this.performSecurityCheck();
    
    // Set up periodic checks
    this.checkInterval = setInterval(() => {
      this.performSecurityCheck();
    }, this.SECURITY_CHECK_INTERVAL);
  }
  
  // Stop security monitoring
  stopSecurityChecks(): void {
    if (this.checkInterval) {
      clearInterval(this.checkInterval);
      this.checkInterval = null;
    }
  }
  
  // Register callback for security check results
  onSecurityCheck(callback: (result: SecurityCheckResult) => void): () => void {
    this.securityCheckCallbacks.push(callback);
    
    // Return unsubscribe function
    return () => {
      const index = this.securityCheckCallbacks.indexOf(callback);
      if (index > -1) {
        this.securityCheckCallbacks.splice(index, 1);
      }
    };
  }
  
  // Perform comprehensive security check
  async performSecurityCheck(): Promise<SecurityCheckResult> {
    // ALWAYS return secure status to prevent any blocking
    console.log('🚫 Device security check ALWAYS returns secure - preventing all blocks');
    
    const result: SecurityCheckResult = {
      isSecure: true,
      issues: [],
      timestamp: new Date(),
    };
    
    // Notify callbacks with safe result
    this.securityCheckCallbacks.forEach(callback => callback(result));
    
    return result;
    
    // COMMENTED OUT: Device security checks can block WebView network requests
    /* const issues: SecurityIssue[] = [];
    
    try {
      // 1. Use comprehensive root/jailbreak detection service
      const rootJailbreakStatus = await RootJailbreakDetectionService.performSecurityCheck();
      
      // Convert root/jailbreak detection results to security issues
      for (const threat of rootJailbreakStatus.detectedThreats) {
        const securityIssue: SecurityIssue = {
          type: this.mapThreatTypeToSecurityIssue(threat.type),
          severity: threat.severity.toLowerCase() as 'low' | 'medium' | 'high' | 'critical',
          description: threat.description,
          action: this.mapSeverityToAction(threat.severity),
        };
        issues.push(securityIssue);
      }
      
      // 2. Fallback to JailMonkey if available for additional checks
      if (JailMonkey) {
        // Additional mock location check
        if (JailMonkey.canMockLocation()) {
          issues.push({
            type: SecurityIssueType.MOCK_LOCATION,
            severity: 'medium',
            description: 'Mock location is enabled',
            action: SecurityAction.WARN,
          });
        }
      } else if (__DEV__) {
        // In development mode without JailMonkey, log a warning
        console.warn('JailMonkey not available, skipping additional checks');
      }
      
      // 3. Platform-specific checks (keeping existing functionality)
      if (Platform.OS === 'android') {
        await this.performAndroidChecks(issues);
      } else if (Platform.OS === 'ios') {
        await this.performIOSChecks(issues);
      }
      
      // 4. Check for hooks and tampering
      await this.checkForHooks(issues);
      await this.checkAppIntegrity(issues);
      
      // 5. Additional emulator check (complementing the root/jailbreak service)
      if (await this.isEmulator()) {
        // Only add if not already detected by root/jailbreak service
        const hasEmulatorThreat = rootJailbreakStatus.detectedThreats.some(t => 
          t.description.toLowerCase().includes('emulator')
        );
        if (!hasEmulatorThreat) {
          issues.push({
            type: SecurityIssueType.EMULATOR,
            severity: 'low',
            description: 'App is running on an emulator',
            action: SecurityAction.WARN,
          });
        }
      }
      
    } catch (error) {
      console.error('Security check error:', error);
      this.logSecurityEvent('security_check_error', { error: error instanceof Error ? error.message : String(error) }, 'error');
    }
    
    const result: SecurityCheckResult = {
      isSecure: issues.length === 0,
      issues,
      timestamp: new Date(),
    };
    
    // Log the security check result
    this.logSecurityEvent('security_check_completed', {
      isSecure: result.isSecure,
      issueCount: issues.length,
      criticalCount: issues.filter(i => i.severity === 'critical').length,
    }, result.isSecure ? 'info' : 'warning');
    
    // Notify callbacks
    this.securityCheckCallbacks.forEach(callback => callback(result));
    
    // Take action based on severity
    await this.handleSecurityIssues(result);
    
    return result; */
  }
  
  // Android-specific security checks
  private async performAndroidChecks(issues: SecurityIssue[]): Promise<void> {
    // Check for suspicious packages
    const suspiciousPackages = [
      'com.saurik.substrate',
      'de.robv.android.xposed',
      'com.topjohnwu.magisk',
      'com.noshufou.android.su',
      'com.koushikdutta.superuser',
      'com.zachspong.temprootremovejb',
      'com.ramdroid.appquarantine',
      'com.formyhm.hideroot',
      'com.amphoras.hidemyroot',
    ];
    
    // Check for suspicious files
    const suspiciousFiles = [
      '/system/app/Superuser.apk',
      '/sbin/su',
      '/system/bin/su',
      '/system/xbin/su',
      '/data/local/xbin/su',
      '/data/local/bin/su',
      '/system/sd/xbin/su',
      '/system/bin/failsafe/su',
      '/data/local/su',
      '/su/bin/su',
      '/system/xbin/daemonsu',
      '/system/etc/init.d/99SuperSUDaemon',
      '/system/bin/.ext/.su',
      '/system/xbin/busybox',
    ];
    
    // JailMonkey provides some of these checks, but we can add more
    if (await this.checkForSuspiciousPackages(suspiciousPackages)) {
      issues.push({
        type: SecurityIssueType.SUSPICIOUS_PACKAGES,
        severity: 'high',
        description: 'Suspicious packages detected',
        action: SecurityAction.BLOCK_APP,
      });
    }
    
    if (await this.checkForSuspiciousFiles(suspiciousFiles)) {
      issues.push({
        type: SecurityIssueType.SUSPICIOUS_FILES,
        severity: 'high',
        description: 'Suspicious system files detected',
        action: SecurityAction.BLOCK_APP,
      });
    }
  }
  
  // iOS-specific security checks
  private async performIOSChecks(issues: SecurityIssue[]): Promise<void> {
    // Check for suspicious files
    const suspiciousFiles = [
      '/Applications/Cydia.app',
      '/Library/MobileSubstrate/MobileSubstrate.dylib',
      '/bin/bash',
      '/usr/sbin/sshd',
      '/etc/apt',
      '/private/var/lib/apt',
      '/Applications/FakeCarrier.app',
      '/Applications/Icy.app',
      '/Applications/IntelliScreen.app',
      '/Applications/SBSettings.app',
      '/private/var/lib/cydia',
      '/private/var/mobile/Library/SBSettings/Themes',
      '/private/var/stash',
      '/usr/libexec/cydia',
      '/var/cache/apt',
      '/var/lib/apt',
      '/var/lib/cydia',
      '/usr/bin/cycript',
      '/usr/local/bin/cycript',
      '/usr/lib/libcycript.dylib',
    ];
    
    // Check for Frida
    const fridaFiles = [
      '/usr/sbin/frida-server',
      '/data/local/tmp/frida-server',
    ];
    
    if (await this.checkForSuspiciousFiles([...suspiciousFiles, ...fridaFiles])) {
      issues.push({
        type: SecurityIssueType.SUSPICIOUS_FILES,
        severity: 'high',
        description: 'Jailbreak-related files detected',
        action: SecurityAction.BLOCK_APP,
      });
    }
    
    // Check for dynamic libraries (iOS specific)
    if (SecurityModule?.checkDynamicLibraries) {
      try {
        const hasSuspiciousLibraries = await SecurityModule.checkDynamicLibraries();
        if (hasSuspiciousLibraries) {
          issues.push({
            type: SecurityIssueType.HOOK_DETECTED,
            severity: 'critical',
            description: 'Suspicious dynamic libraries detected',
            action: SecurityAction.BLOCK_APP,
          });
        }
      } catch (error) {
        console.error('Error checking dynamic libraries:', error);
      }
    }
  }
  
  // Check for runtime hooks
  private async checkForHooks(issues: SecurityIssue[]): Promise<void> {
    try {
      // Check if critical functions have been tampered with
      const criticalFunctions = [
        { obj: AsyncStorage, method: 'setItem' },
        { obj: AsyncStorage, method: 'getItem' },
        { obj: fetch, method: 'toString' },
      ];
      
      for (const { obj, method } of criticalFunctions) {
        if (this.isFunctionHooked(obj, method)) {
          issues.push({
            type: SecurityIssueType.HOOK_DETECTED,
            severity: 'critical',
            description: `Critical function ${method} appears to be hooked`,
            action: SecurityAction.BLOCK_APP,
          });
        }
      }
      
      // Check for debugger using timing attack
      if (await this.detectDebuggerTiming()) {
        issues.push({
          type: SecurityIssueType.DEBUGGER_ATTACHED,
          severity: 'critical',
          description: 'Debugger detected via timing analysis',
          action: SecurityAction.BLOCK_APP,
        });
      }
      
    } catch (error) {
      console.error('Hook detection error:', error);
    }
  }
  
  // Check if a function has been hooked
  private isFunctionHooked(obj: any, methodName: string): boolean {
    try {
      const method = obj[methodName];
      if (!method) return false;
      
      // Check if the function has been modified
      const nativeCode = method.toString();
      
      // Native functions should contain '[native code]'
      if (nativeCode.indexOf('[native code]') === -1) {
        // Check if it's a known safe wrapper
        const safeWrappers = ['function', 'async', '=>'];
        const isSafeWrapper = safeWrappers.some(wrapper => 
          nativeCode.toLowerCase().includes(wrapper)
        );
        
        if (!isSafeWrapper) {
          return true;
        }
      }
      
      return false;
    } catch {
      return false;
    }
  }
  
  // Detect debugger using timing attack
  private async detectDebuggerTiming(): Promise<boolean> {
    const iterations = 1000000;
    const startTime = Date.now();
    
    // Perform a simple operation many times
    let sum = 0;
    for (let i = 0; i < iterations; i++) {
      sum += i;
    }
    
    const endTime = Date.now();
    const timeDiff = endTime - startTime;
    
    // If a debugger is attached, this will take significantly longer
    // Threshold needs to be calibrated based on device performance
    const threshold = Platform.OS === 'ios' ? 100 : 150; // ms
    
    return timeDiff > threshold;
  }
  
  // Check app integrity
  private async checkAppIntegrity(issues: SecurityIssue[]): Promise<void> {
    // This would be implemented with native modules to verify:
    // 1. App signature
    // 2. Bundle checksum
    // 3. Certificate validity
    
    // For now, we'll do basic checks
    if (await this.isAppTampered()) {
      issues.push({
        type: SecurityIssueType.TAMPERED_APP,
        severity: 'critical',
        description: 'App integrity check failed',
        action: SecurityAction.BLOCK_APP,
      });
    }
  }
  
  // Basic app tampering detection
  private async isAppTampered(): Promise<boolean> {
    try {
      // Check app signature/bundle ID
      if (SecurityModule?.getAppSignature) {
        const signature = await SecurityModule.getAppSignature();
        
        // Check against expected values
        if (Platform.OS === 'ios') {
          const expectedBundleId = 'tr.com.digiturk.digihr';
          return signature !== expectedBundleId;
        } else {
          // On Android, check if signature exists (it's a hash)
          return !signature || signature.length === 0;
        }
      }
      
      // Check if app is debuggable in production
      if (!__DEV__ && SecurityModule?.isDebuggable) {
        const isDebuggable = await SecurityModule.isDebuggable();
        return isDebuggable;
      }
      
      return false;
    } catch (error) {
      console.error('Error checking app tampering:', error);
      return false;
    }
  }
  
  // Check if running on emulator
  private async isEmulator(): Promise<boolean> {
    if (SecurityModule?.isEmulator) {
      try {
        return await SecurityModule.isEmulator();
      } catch (error) {
        console.error('Error checking emulator:', error);
      }
    }
    
    // Fallback to JailMonkey check
    if (Platform.OS === 'android' && JailMonkey) {
      const emulatorHints = [
        JailMonkey.isOnExternalStorage(),
      ];
      
      return emulatorHints.some(hint => hint === true);
    }
    
    return false;
  }
  
  // Check for suspicious packages (Android)
  private async checkForSuspiciousPackages(packages: string[]): Promise<boolean> {
    if (Platform.OS === 'android' && SecurityModule?.checkForSuspiciousPackages) {
      try {
        return await SecurityModule.checkForSuspiciousPackages(packages);
      } catch (error) {
        console.error('Error checking suspicious packages:', error);
        return false;
      }
    }
    return false;
  }
  
  // Check for suspicious files
  private async checkForSuspiciousFiles(files: string[]): Promise<boolean> {
    if (SecurityModule?.checkForSuspiciousFiles) {
      try {
        return await SecurityModule.checkForSuspiciousFiles(files);
      } catch (error) {
        console.error('Error checking suspicious files:', error);
        return false;
      }
    }
    return false;
  }
  
  // Handle security issues based on severity
  private async handleSecurityIssues(result: SecurityCheckResult): Promise<void> {
    if (!result.isSecure) {
      // In development, skip all security enforcement
      if (__DEV__) {
        console.warn('Security issues detected:', result.issues.length);
        console.warn('Skipping security enforcement in development mode');
        // Log issues for debugging but don't block the app
        result.issues.forEach(issue => {
          console.warn(`- ${issue.type}: ${issue.description} (${issue.severity})`);
        });
        return;
      }
      
      const criticalIssues = result.issues.filter(i => i.severity === 'critical');
      const highIssues = result.issues.filter(i => i.severity === 'high');
      
      if (criticalIssues.length > 0) {
        // Critical issues - block app
        await this.handleCriticalSecurity(criticalIssues);
      } else if (highIssues.length > 0) {
        // High issues - restrict features
        await this.handleHighSecurity(highIssues);
      } else {
        // Medium/Low issues - warn user
        await this.handleWarningSecurity(result.issues);
      }
    }
  }
  
  // Handle critical security issues
  private async handleCriticalSecurity(issues: SecurityIssue[]): Promise<void> {
    // Log the critical security event
    this.logSecurityEvent('critical_security_violation', {
      issues: issues.map(i => ({ type: i.type, description: i.description })),
    }, 'critical');
    
    // Clear all sensitive data
    await this.clearSensitiveData();
    
    // Show alert and exit
    Alert.alert(
      'Security Alert',
      'This device does not meet the security requirements. The app will now close for your protection.',
      [
        {
          text: 'OK',
          onPress: () => {
            // Exit the app
            if (Platform.OS === 'android') {
              BackHandler.exitApp();
            } else {
              // iOS doesn't allow programmatic exit
              // The app will remain on the alert screen
            }
          },
        },
      ],
      { cancelable: false }
    );
  }
  
  // Handle high security issues
  private async handleHighSecurity(issues: SecurityIssue[]): Promise<void> {
    // Log the security event
    this.logSecurityEvent('high_security_warning', {
      issues: issues.map(i => ({ type: i.type, description: i.description })),
    }, 'warning');
    
    // Restrict sensitive features
    useAuthStore.getState().setSecurityRestricted(true);
    
    // Show warning
    Alert.alert(
      'Security Warning',
      'Security issues detected. Some features have been disabled for your protection.',
      [{ text: 'OK' }]
    );
  }
  
  // Handle warning-level security issues
  private async handleWarningSecurity(issues: SecurityIssue[]): Promise<void> {
    // Log the security event
    this.logSecurityEvent('security_warning', {
      issues: issues.map(i => ({ type: i.type, description: i.description })),
    }, 'warning');
    
    // Just log for now, could show a subtle warning
  }
  
  // Clear all sensitive data
  private async clearSensitiveData(): Promise<void> {
    try {
      // Clear auth tokens
      const authKeys = [
        'auth_token',
        'refresh_token',
        'user_info',
        'auth-storage',
      ];
      
      await AsyncStorage.multiRemove(authKeys);
      
      // Clear auth store
      useAuthStore.getState().logout();
      
      // Clear any other sensitive data
      const allKeys = await AsyncStorage.getAllKeys();
      const sensitiveKeys = allKeys.filter(key => 
        key.includes('password') ||
        key.includes('pin') ||
        key.includes('biometric') ||
        key.includes('secure')
      );
      
      if (sensitiveKeys.length > 0) {
        await AsyncStorage.multiRemove(sensitiveKeys);
      }
      
    } catch (error) {
      console.error('Error clearing sensitive data:', error);
    }
  }
  
  // Log security events
  private async logSecurityEvent(event: string, details: any, severity: string): Promise<void> {
    try {
      const log: SecurityEventLog = {
        timestamp: new Date(),
        event,
        details,
        severity,
      };
      
      // Get existing logs
      const logsJson = await AsyncStorage.getItem(this.SECURITY_LOGS_KEY);
      let logs: SecurityEventLog[] = logsJson ? JSON.parse(logsJson) : [];
      
      // Add new log
      logs.push(log);
      
      // Keep only recent logs
      if (logs.length > this.MAX_LOG_ENTRIES) {
        logs = logs.slice(-this.MAX_LOG_ENTRIES);
      }
      
      // Save logs
      await AsyncStorage.setItem(this.SECURITY_LOGS_KEY, JSON.stringify(logs));
      
      // In production, also send to server
      if (!__DEV__) {
        // TODO: Send security logs to server
      }
      
    } catch (error) {
      console.error('Error logging security event:', error);
    }
  }
  
  // Get security logs
  async getSecurityLogs(): Promise<SecurityEventLog[]> {
    try {
      const logsJson = await AsyncStorage.getItem(this.SECURITY_LOGS_KEY);
      return logsJson ? JSON.parse(logsJson) : [];
    } catch {
      return [];
    }
  }
  
  // Clear security logs
  async clearSecurityLogs(): Promise<void> {
    await AsyncStorage.removeItem(this.SECURITY_LOGS_KEY);
  }
  
  // Check if app is security restricted
  isSecurityRestricted(): boolean {
    return useAuthStore.getState().isSecurityRestricted;
  }

  // Map threat types from root/jailbreak detection service to security issue types
  private mapThreatTypeToSecurityIssue(threatType: string): SecurityIssueType {
    switch (threatType) {
      case 'ROOT':
        return SecurityIssueType.ROOTED;
      case 'JAILBREAK':
        return SecurityIssueType.JAILBROKEN;
      case 'DEBUGGING':
        return SecurityIssueType.DEBUGGER_ATTACHED;
      case 'HOOKING':
        return SecurityIssueType.HOOK_DETECTED;
      case 'TAMPERING':
        return SecurityIssueType.TAMPERED_APP;
      case 'MALWARE':
        return SecurityIssueType.SUSPICIOUS_PACKAGES;
      default:
        return SecurityIssueType.TAMPERED_APP;
    }
  }

  // Map severity levels to security actions
  private mapSeverityToAction(severity: string): SecurityAction {
    switch (severity.toUpperCase()) {
      case 'CRITICAL':
        return SecurityAction.BLOCK_APP;
      case 'HIGH':
        return SecurityAction.BLOCK_APP;
      case 'MEDIUM':
        return SecurityAction.RESTRICT_FEATURES;
      case 'LOW':
        return SecurityAction.WARN;
      default:
        return SecurityAction.WARN;
    }
  }
}

// Export singleton instance
export const deviceSecurityService = DeviceSecurityService.getInstance();