import { certificatePinning } from './enhancedCertificatePinningService';
import { deviceSecurityService } from './deviceSecurityService';
import { Platform } from 'react-native';

interface RequestConfig extends RequestInit {
  url: string;
  skipPinning?: boolean;
}

interface InterceptorConfig {
  onPinningFailure?: (url: string, error: string) => void;
  onSecurityViolation?: (url: string, violation: any) => void;
  enableLogging?: boolean;
}

class NetworkInterceptor {
  private static instance: NetworkInterceptor;
  private config: InterceptorConfig;
  private originalFetch: typeof fetch;
  private isEnabled: boolean = false;

  private constructor() {
    this.config = {
      enableLogging: __DEV__,
    };
    
    // Store original fetch but don't modify it in dev mode
    this.originalFetch = global.fetch;
    
    // CRITICAL FIX: Completely disable interceptor in ALL MODES temporarily
    console.log('🚫 Network interceptor COMPLETELY DISABLED');
    this.isEnabled = false;
    // Don't modify global.fetch at all
    return;
    
    // COMMENTED OUT: Disabling all network interception
    /*
    if (__DEV__) {
      console.log('🚫 Network interceptor COMPLETELY DISABLED in development mode');
      this.isEnabled = false;
      // Don't modify global.fetch at all
      return;
    }
    
    this.isEnabled = true;
    this.setupInterceptor();
    */
  }

  static getInstance(): NetworkInterceptor {
    if (!NetworkInterceptor.instance) {
      NetworkInterceptor.instance = new NetworkInterceptor();
    }
    return NetworkInterceptor.instance;
  }

  private setupInterceptor(): void {
    // COMMENTED OUT: Network interceptor can break WebView functionality
    // Override global fetch
    /* global.fetch = async (input: RequestInfo | URL, init?: RequestInit): Promise<Response> => {
      const url = typeof input === 'string' ? input : input instanceof URL ? input.toString() : input.url || '';
      const startTime = Date.now();
      
      console.log('===== NETWORK INTERCEPTOR START =====');
      console.log('Timestamp:', new Date().toISOString());
      console.log('URL:', url);
      console.log('Method:', init?.method || 'GET');
      
      try {
        // Pre-request security checks
        console.log('Performing pre-request security checks...');
        const preCheckStart = Date.now();
        await this.performPreRequestChecks(url);
        console.log('Pre-request checks took:', Date.now() - preCheckStart, 'ms');
        
        // Certificate pinning validation
        if (this.shouldValidateCertificate(url)) {
          console.log('Certificate pinning validation required for:', url);
          console.log('Certificate pinning enabled:', certificatePinning.isPinningEnabled());
          const pinningStart = Date.now();
          const pinningResult = await certificatePinning.validateCertificate(url);
          console.log('Certificate pinning took:', Date.now() - pinningStart, 'ms');
          console.log('Pinning result:', pinningResult);
          
          if (!pinningResult.isValid) {
            const error = new Error(`Certificate pinning failed: ${pinningResult.reason}`);
            
            if (this.config.onPinningFailure) {
              this.config.onPinningFailure(url, pinningResult.reason || 'Unknown error');
            }
            
            // Log the failure
            this.logSecurityEvent('certificate_pinning_failed', {
              url,
              reason: pinningResult.reason,
              timestamp: new Date().toISOString(),
            });
            
            throw error;
          }
        } else {
          console.log('Certificate pinning skipped for:', url);
        }
        
        // Add security headers
        const secureInit = this.addSecurityHeaders(init);
        
        // Log request if enabled
        if (this.config.enableLogging) {
          console.log('Network request:', {
            url,
            method: secureInit.method || 'GET',
            headers: this.sanitizeHeaders(secureInit.headers),
          });
        }
        
        // Make the actual request
        console.log('Making actual fetch request...');
        const fetchStart = Date.now();
        const response = await this.originalFetch(input, secureInit);
        console.log('Fetch took:', Date.now() - fetchStart, 'ms');
        
        // Post-response security checks
        console.log('Performing post-response security checks...');
        const postCheckStart = Date.now();
        await this.performPostResponseChecks(url, response);
        console.log('Post-response checks took:', Date.now() - postCheckStart, 'ms');
        
        const totalTime = Date.now() - startTime;
        console.log('===== NETWORK INTERCEPTOR COMPLETE =====');
        console.log('Total interceptor time:', totalTime, 'ms');
        console.log('Response status:', response.status);
        console.log('========================================');
        
        return response;
        
      } catch (error) {
        const totalTime = Date.now() - startTime;
        console.error('===== NETWORK INTERCEPTOR ERROR =====');
        console.error('Total time before error:', totalTime, 'ms');
        console.error('Error:', error);
        console.error('=====================================');
        
        // Log network errors
        this.logSecurityEvent('network_error', {
          url,
          error: error instanceof Error ? error.message : String(error),
          timestamp: new Date().toISOString(),
        });
        
        throw error;
      }
    }; */
    console.log('🚫 Network interceptor setupInterceptor() method disabled - WebView protection');
  }

  private async performPreRequestChecks(url: string): Promise<void> {
    // Check device security status
    const securityCheck = await deviceSecurityService.performSecurityCheck();
    
    console.log('Pre-request security check:', {
      isSecure: securityCheck.isSecure,
      issueCount: securityCheck.issues.length,
      issues: securityCheck.issues.map(i => ({ type: i.type, severity: i.severity, description: i.description }))
    });
    
    if (!securityCheck.isSecure) {
      const criticalIssues = securityCheck.issues.filter(i => i.severity === 'critical');
      
      if (criticalIssues.length > 0) {
        console.error('BLOCKING request due to critical security issues:', criticalIssues);
        // Block requests on critically compromised devices
        throw new Error('Network requests blocked due to device security issues');
      } else {
        console.log('Security issues detected but not critical - allowing request');
      }
    }
    
    // Check if URL is in blocklist
    if (this.isBlockedUrl(url)) {
      throw new Error('Request to blocked URL');
    }
  }

  private async performPostResponseChecks(url: string, response: Response): Promise<void> {
    // Check for suspicious response headers
    const suspiciousHeaders = [
      'x-powered-by',
      'server',
      'x-aspnet-version',
    ];
    
    suspiciousHeaders.forEach(header => {
      const value = response.headers.get(header);
      if (value) {
        this.logSecurityEvent('suspicious_response_header', {
          url,
          header,
          value,
          timestamp: new Date().toISOString(),
        });
      }
    });
    
    // Check for security headers
    const securityHeaders = [
      'strict-transport-security',
      'x-content-type-options',
      'x-frame-options',
      'x-xss-protection',
      'content-security-policy',
    ];
    
    const missingHeaders = securityHeaders.filter(header => !response.headers.get(header));
    
    if (missingHeaders.length > 0 && this.isProductionUrl(url)) {
      this.logSecurityEvent('missing_security_headers', {
        url,
        missingHeaders,
        timestamp: new Date().toISOString(),
      });
    }
  }

  private shouldValidateCertificate(url: string): boolean {
    try {
      const urlObj = new URL(url);
      
      // Only validate HTTPS URLs
      if (urlObj.protocol !== 'https:') {
        return false;
      }
      
      // Skip validation for localhost and development
      if (urlObj.hostname === 'localhost' || urlObj.hostname === '127.0.0.1') {
        return false;
      }
      
      // Skip if explicitly disabled
      if (!certificatePinning.isPinningEnabled()) {
        return false;
      }
      
      return true;
    } catch {
      return false;
    }
  }

  private addSecurityHeaders(init?: RequestInit): RequestInit {
    const headers = new Headers(init?.headers);
    
    // Add security headers if not present
    if (!headers.has('X-Requested-With')) {
      headers.set('X-Requested-With', 'XMLHttpRequest');
    }
    
    // Add platform header
    headers.set('X-Platform', Platform.OS);
    headers.set('X-Platform-Version', Platform.Version.toString());
    
    // Add app version header
    headers.set('X-App-Version', '1.0.0'); // Should come from app config
    
    return {
      ...init,
      headers,
    };
  }

  private sanitizeHeaders(headers?: HeadersInit): Record<string, string> {
    const sanitized: Record<string, string> = {};
    
    if (!headers) return sanitized;
    
    const headerObj = headers instanceof Headers ? 
      Object.fromEntries(headers.entries()) : 
      headers as Record<string, string>;
    
    // Hide sensitive headers in logs
    const sensitiveHeaders = ['authorization', 'cookie', 'x-api-key'];
    
    Object.entries(headerObj).forEach(([key, value]) => {
      if (sensitiveHeaders.includes(key.toLowerCase())) {
        sanitized[key] = '[REDACTED]';
      } else {
        sanitized[key] = value;
      }
    });
    
    return sanitized;
  }

  private isBlockedUrl(url: string): boolean {
    const blockedPatterns = [
      // Add any blocked URL patterns here
      /^http:\/\//, // Block all HTTP in production
    ];
    
    if (!__DEV__) {
      return blockedPatterns.some(pattern => pattern.test(url));
    }
    
    return false;
  }

  private isProductionUrl(url: string): boolean {
    try {
      const urlObj = new URL(url);
      return urlObj.hostname.includes('digiturk.com.tr');
    } catch {
      return false;
    }
  }

  private logSecurityEvent(event: string, details: any): void {
    if (this.config.enableLogging) {
      console.log(`[NetworkSecurity] ${event}:`, details);
    }
    
    // In production, send to logging service
    if (!__DEV__) {
      // TODO: Send to remote logging service
    }
  }

  /**
   * Configure the interceptor
   */
  configure(config: InterceptorConfig): void {
    // Skip configuration if disabled in dev mode
    if (!this.isEnabled) {
      console.log('🚫 Network interceptor configuration skipped - disabled in dev mode');
      return;
    }
    this.config = { ...this.config, ...config };
  }

  /**
   * Make a secure fetch request with certificate pinning
   */
  async secureFetch(url: string, options?: RequestInit): Promise<Response> {
    return fetch(url, options);
  }

  /**
   * Temporarily disable the interceptor (for debugging)
   */
  disable(): void {
    if (__DEV__) {
      console.log('🚫 Network interceptor disabled via disable() method');
      global.fetch = this.originalFetch;
    }
  }

  /**
   * Re-enable the interceptor
   */
  enable(): void {
    if (__DEV__) {
      console.log('🚫 Network interceptor cannot be re-enabled in development mode');
      return;
    }
    this.setupInterceptor();
  }
}

export const networkInterceptor = NetworkInterceptor.getInstance();

// Export convenience function
export const secureFetch = (url: string, options?: RequestInit): Promise<Response> => {
  return networkInterceptor.secureFetch(url, options);
};