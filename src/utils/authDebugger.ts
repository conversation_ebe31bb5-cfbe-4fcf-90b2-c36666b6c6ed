/**
 * Authentication Debugger Utility
 * 
 * Comprehensive debugging tool to diagnose Android authentication issues
 */

import DeviceInfo from 'react-native-device-info';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { getMobileApiUrl, getDigiflowApiUrl } from '../services/configService';

export interface AuthDebugInfo {
  timestamp: string;
  platform: string;
  deviceInfo: {
    systemName: string;
    systemVersion: string;
    appVersion: string;
    buildNumber: string;
    deviceId: string;
    deviceName: string;
  };
  networkInfo: {
    mobileApiUrl: string;
    digiflowApiUrl: string;
    isConnected: boolean;
  };
  storageInfo: {
    hasToken: boolean;
    hasRefreshToken: boolean;
    hasUserInfo: boolean;
    tokenExpiry?: string;
    storageKeys: string[];
  };
  apiTests: {
    mobileApiReachable: boolean;
    mobileApiResponse?: string;
    mobileApiError?: string;
    authEndpointTest?: {
      status: number;
      headers: Record<string, string>;
      body: string;
      isNtlm: boolean;
    };
  };
}

export class AuthDebugger {
  private static instance: AuthDebugger;

  static getInstance(): AuthDebugger {
    if (!AuthDebugger.instance) {
      AuthDebugger.instance = new AuthDebugger();
    }
    return AuthDebugger.instance;
  }

  /**
   * Collect comprehensive authentication debug information
   */
  async collectDebugInfo(): Promise<AuthDebugInfo> {
    console.log('🔍 Starting authentication debug collection...');

    const debugInfo: AuthDebugInfo = {
      timestamp: new Date().toISOString(),
      platform: 'android',
      deviceInfo: await this.getDeviceInfo(),
      networkInfo: await this.getNetworkInfo(),
      storageInfo: await this.getStorageInfo(),
      apiTests: await this.testApiEndpoints(),
    };

    console.log('🔍 Debug info collected:', JSON.stringify(debugInfo, null, 2));
    return debugInfo;
  }

  /**
   * Get device information
   */
  private async getDeviceInfo() {
    return {
      systemName: DeviceInfo.getSystemName(),
      systemVersion: DeviceInfo.getSystemVersion(),
      appVersion: DeviceInfo.getVersion(),
      buildNumber: DeviceInfo.getBuildNumber(),
      deviceId: await DeviceInfo.getUniqueId(),
      deviceName: await DeviceInfo.getDeviceName(),
    };
  }

  /**
   * Get network configuration information
   */
  private async getNetworkInfo() {
    return {
      mobileApiUrl: getMobileApiUrl(),
      digiflowApiUrl: getDigiflowApiUrl(),
      isConnected: true, // TODO: Add actual network connectivity check
    };
  }

  /**
   * Get authentication storage information
   */
  private async getStorageInfo() {
    try {
      const allKeys = await AsyncStorage.getAllKeys();
      const authKeys = allKeys.filter(key => 
        key.toLowerCase().includes('auth') ||
        key.toLowerCase().includes('token') ||
        key.toLowerCase().includes('user') ||
        key.toLowerCase().includes('login')
      );

      const hasToken = await AsyncStorage.getItem('unified_auth_token') !== null;
      const hasRefreshToken = await AsyncStorage.getItem('unified_refresh_token') !== null;
      const hasUserInfo = await AsyncStorage.getItem('unified_user_info') !== null;
      const tokenExpiry = await AsyncStorage.getItem('unified_token_expiry');

      return {
        hasToken,
        hasRefreshToken,
        hasUserInfo,
        tokenExpiry: tokenExpiry || undefined,
        storageKeys: authKeys,
      };
    } catch (error) {
      console.error('Error getting storage info:', error);
      return {
        hasToken: false,
        hasRefreshToken: false,
        hasUserInfo: false,
        storageKeys: [],
      };
    }
  }

  /**
   * Test API endpoints for reachability and authentication
   */
  private async testApiEndpoints() {
    const mobileApiUrl = getMobileApiUrl();
    const authEndpoint = `${mobileApiUrl}/auth/login`;

    console.log('🔍 Testing API endpoints...');
    console.log('Mobile API URL:', mobileApiUrl);
    console.log('Auth endpoint:', authEndpoint);

    try {
      // Test basic connectivity to mobile API
      const response = await fetch(authEndpoint, {
        method: 'OPTIONS',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const responseText = await response.text();
      const headers: Record<string, string> = {};
      response.headers.forEach((value, key) => {
        headers[key] = value;
      });

      // Check if response indicates NTLM authentication
      const isNtlm = responseText.includes('NTLM') || 
                    responseText.includes('Windows Authentication') ||
                    headers['www-authenticate']?.includes('NTLM') ||
                    response.status === 401;

      return {
        mobileApiReachable: response.status < 500,
        authEndpointTest: {
          status: response.status,
          headers,
          body: responseText.substring(0, 500),
          isNtlm,
        },
      };
    } catch (error) {
      console.error('API endpoint test failed:', error);
      return {
        mobileApiReachable: false,
        mobileApiError: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Test authentication with sample credentials
   */
  async testAuthentication(username: string, password: string): Promise<{
    success: boolean;
    error?: string;
    response?: any;
    isNtlmIssue?: boolean;
  }> {
    console.log('🔍 Testing authentication...');

    try {
      const mobileApiUrl = getMobileApiUrl();
      const loginUrl = `${mobileApiUrl}/auth/login`;

      const deviceInfo = await this.getDeviceInfo();
      const userAgent = `DigiHR/${deviceInfo.appVersion} (${deviceInfo.systemName} ${deviceInfo.systemVersion}; Build/${deviceInfo.buildNumber})`;

      const response = await fetch(loginUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-Mobile-App': 'true',
          'X-Is-Mobile': 'true',
          'Accept': 'application/json',
          'User-Agent': userAgent,
        },
        body: JSON.stringify({
          Username: username,
          Password: password,
          DeviceId: deviceInfo.deviceId,
          DeviceName: deviceInfo.deviceName,
        }),
      });

      const responseText = await response.text();
      
      // Check for NTLM authentication issues
      const isNtlmIssue = responseText.includes('NTLM') || 
                         responseText.includes('Windows Authentication') ||
                         response.status === 401;

      if (!response.ok) {
        return {
          success: false,
          error: `HTTP ${response.status}: ${responseText.substring(0, 200)}`,
          isNtlmIssue,
        };
      }

      try {
        const jsonResponse = JSON.parse(responseText);
        return {
          success: true,
          response: jsonResponse,
        };
      } catch (parseError) {
        return {
          success: false,
          error: `Failed to parse JSON response: ${responseText.substring(0, 200)}`,
          isNtlmIssue: true,
        };
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Generate a comprehensive debug report
   */
  async generateDebugReport(): Promise<string> {
    const debugInfo = await this.collectDebugInfo();
    
    let report = `
# Android Authentication Debug Report
Generated: ${debugInfo.timestamp}

## Device Information
- Platform: ${debugInfo.deviceInfo.systemName} ${debugInfo.deviceInfo.systemVersion}
- App Version: ${debugInfo.deviceInfo.appVersion} (Build ${debugInfo.deviceInfo.buildNumber})
- Device ID: ${debugInfo.deviceInfo.deviceId}
- Device Name: ${debugInfo.deviceInfo.deviceName}

## Network Configuration
- Mobile API URL: ${debugInfo.networkInfo.mobileApiUrl}
- Digiflow API URL: ${debugInfo.networkInfo.digiflowApiUrl}
- Network Connected: ${debugInfo.networkInfo.isConnected}

## Storage Status
- Has Token: ${debugInfo.storageInfo.hasToken}
- Has Refresh Token: ${debugInfo.storageInfo.hasRefreshToken}
- Has User Info: ${debugInfo.storageInfo.hasUserInfo}
- Token Expiry: ${debugInfo.storageInfo.tokenExpiry || 'Not set'}
- Auth-related Keys: ${debugInfo.storageInfo.storageKeys.join(', ')}

## API Endpoint Tests
- Mobile API Reachable: ${debugInfo.apiTests.mobileApiReachable}
`;

    if (debugInfo.apiTests.authEndpointTest) {
      const test = debugInfo.apiTests.authEndpointTest;
      report += `
- Auth Endpoint Status: ${test.status}
- NTLM Detected: ${test.isNtlm}
- Response Headers: ${JSON.stringify(test.headers, null, 2)}
- Response Body Preview: ${test.body}
`;
    }

    if (debugInfo.apiTests.mobileApiError) {
      report += `
- API Error: ${debugInfo.apiTests.mobileApiError}
`;
    }

    return report;
  }
}

export const authDebugger = AuthDebugger.getInstance();
