import React, { useState, useRef } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
  TextInputProps,
  Animated,
  TouchableWithoutFeedback,
} from 'react-native';
import Icon from './Icon';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

interface InputFieldProps extends TextInputProps {
  label: string;
  leftIcon?: string; // Icon name in format "sprite/icon"
  value: string;
  onChangeText: (text: string) => void;
  placeholder?: string;
  secureTextEntry?: boolean;
  errorMessage?: string;
  successMessage?: string;
  hint?: string;
  onClear?: () => void;
  showInfoIcon?: boolean;
  onInfoPress?: () => void;
  autoCapitalize?: 'none' | 'sentences' | 'words' | 'characters';
  keyboardType?: 'default' | 'email-address' | 'numeric' | 'phone-pad';
  tooltipText?: string; // New prop for tooltip content
  showTooltipOnLeftIcon?: boolean; // New prop to enable tooltip on left icon
}

const InputField: React.FC<InputFieldProps> = ({
  label,
  leftIcon = 'essentials/User', // Default icon
  value,
  onChangeText,
  placeholder,
  secureTextEntry = false,
  errorMessage,
  successMessage,
  hint,
  onClear,
  showInfoIcon = false,
  onInfoPress,
  autoCapitalize = 'none',
  keyboardType = 'default',
  tooltipText,
  showTooltipOnLeftIcon = false,
  ...textInputProps
}) => {
  const [isFocused, setIsFocused] = useState(false);
  const [showTooltip, setShowTooltip] = useState(false);
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const translateY = useRef(new Animated.Value(-10)).current;

  const handleClear = () => {
    onChangeText('');
    if (onClear) {
      onClear();
    }
  };

  const handleTooltipToggle = () => {
    const newShowState = !showTooltip;
    setShowTooltip(newShowState);

    if (newShowState) {
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 200,
          useNativeDriver: true,
        }),
        Animated.timing(translateY, {
          toValue: 0,
          duration: 200,
          useNativeDriver: true,
        }),
      ]).start();
    } else {
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 0,
          duration: 150,
          useNativeDriver: true,
        }),
        Animated.timing(translateY, {
          toValue: -10,
          duration: 150,
          useNativeDriver: true,
        }),
      ]).start();
    }
  };

  const closeTooltip = () => {
    setShowTooltip(false);
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 0,
        duration: 150,
        useNativeDriver: true,
      }),
      Animated.timing(translateY, {
        toValue: -10,
        duration: 150,
        useNativeDriver: true,
      }),
    ]).start();
  };

  const hasError = !!errorMessage;
  const hasSuccess = !!successMessage;
  const showClearButton = value.length > 0 && !secureTextEntry;
  const showLeftTooltip = showTooltipOnLeftIcon && tooltipText;

  // Determine which icon to use - Info icon if tooltip is enabled, otherwise the specified icon
  const iconToDisplay = showLeftTooltip ? 'essentials/Info Light' : leftIcon;

  return (
    <View style={styles.container}>
      {/* Label Row */}
      <View style={styles.labelRow}>
        <Text style={[
          styles.label,
          hasError && styles.labelError,
          hasSuccess && styles.labelSuccess,
        ]}>
          {label}
        </Text>
        {showInfoIcon && !showTooltipOnLeftIcon && (
          <TouchableOpacity onPress={onInfoPress}>
            <Icon name="essentials/Info Light" size={12} color="#6B7280" />
          </TouchableOpacity>
        )}
      </View>

      {/* Input Container */}
      <View style={[
        styles.inputContainer,
        isFocused && styles.inputContainerFocused,
        hasError && styles.inputContainerError,
        hasSuccess && styles.inputContainerSuccess,
      ]}>
        {/* Left Icon */}
        {showLeftTooltip ? (
          <TouchableOpacity onPress={handleTooltipToggle} style={styles.leftIconContainer}>
            <Icon
              name={iconToDisplay}
              size={16}
              color={isFocused ? '#7C3AED' : '#9CA3AF'}
            />
          </TouchableOpacity>
        ) : (
          <View style={styles.leftIconContainer}>
            <Icon
              name={iconToDisplay}
              size={16}
              color={isFocused ? '#7C3AED' : '#9CA3AF'}
            />
          </View>
        )}

        {/* Text Input */}
        <TextInput
          {...textInputProps}
          style={styles.textInput}
          value={value}
          onChangeText={onChangeText}
          placeholder={placeholder}
          placeholderTextColor="#9CA3AF"
          secureTextEntry={secureTextEntry}
          onFocus={() => setIsFocused(true)}
          onBlur={() => setIsFocused(false)}
          autoCapitalize={autoCapitalize}
          keyboardType={keyboardType}
        />

        {/* Right Clear Button */}
        {showClearButton && (
          <TouchableOpacity onPress={handleClear} style={styles.clearButton}>
            <Icon name="essentials/Cross Light" size={16} color="#9CA3AF" />
          </TouchableOpacity>
        )}
      </View>

      {/* Tooltip Modal Overlay */}
      {showTooltip && tooltipText && (
        <>
          <TouchableWithoutFeedback onPress={closeTooltip}>
            <Animated.View
              style={[
                styles.tooltipOverlay,
                {
                  opacity: fadeAnim,
                },
              ]}
            />
          </TouchableWithoutFeedback>
          <Animated.View
            style={[
              styles.tooltipContainer,
              {
                opacity: fadeAnim,
                transform: [{ translateY }],
              },
            ]}
            pointerEvents="box-none"
          >
            <View style={styles.tooltip}>
              <Text style={styles.tooltipText}>{tooltipText}</Text>
            </View>
            <View style={styles.arrow} />
          </Animated.View>
        </>
      )}

      {/* Hint Text */}
      {hint && !hasError && !hasSuccess && (
        <Text style={styles.hint}>{hint}</Text>
      )}

      {/* Error Message */}
      {hasError && (
        <Text style={styles.errorText}>{errorMessage}</Text>
      )}

      {/* Success Message */}
      {hasSuccess && (
        <Text style={styles.successText}>{successMessage}</Text>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: screenHeight * 0.02, // 2% of screen height
    position: 'relative',
    width: '100%', // Ensure full width
  },
  labelRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: screenHeight * 0.008, // 0.8% of screen height
  },
  label: {
    fontSize: Math.min(screenWidth * 0.032, 14), // Max 14px
    color: '#374151',
    fontWeight: '500',
  },
  labelError: {
    color: '#EF4444',
  },
  labelSuccess: {
    color: '#10B981',
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F9FAFB',
    borderWidth: 1,
    borderColor: '#E5E7EB',
    borderRadius: 12,
    paddingHorizontal: 12,
    height: Math.max(screenHeight * 0.06, 50), // Min 50px, max 6% height
    width: '100%', // Ensure full width
  },
  inputContainerFocused: {
    borderColor: '#7C3AED',
    backgroundColor: '#FFFFFF',
  },
  inputContainerError: {
    borderColor: '#EF4444',
    backgroundColor: '#FEF2F2',
  },
  inputContainerSuccess: {
    borderColor: '#10B981',
    backgroundColor: '#F0FDF4',
  },
  leftIconContainer: {
    marginRight: 12,
  },
  textInput: {
    flex: 1,
    fontSize: Math.min(screenWidth * 0.035, 16), // Max 16px
    color: '#1F2937',
    paddingVertical: 0, // Remove default padding
  },
  clearButton: {
    padding: 4,
    marginLeft: 8,
  },
  hint: {
    fontSize: Math.min(screenWidth * 0.03, 12), // Max 12px
    color: '#6B7280',
    marginTop: 4,
  },
  errorText: {
    fontSize: Math.min(screenWidth * 0.03, 12), // Max 12px
    color: '#EF4444',
    marginTop: 4,
  },
  successText: {
    fontSize: Math.min(screenWidth * 0.03, 12), // Max 12px
    color: '#10B981',
    marginTop: 4,
  },
  // Tooltip styles
  tooltipOverlay: {
    position: 'absolute',
    top: -1000,
    left: -1000,
    right: -1000,
    bottom: -1000,
    backgroundColor: 'rgba(0, 0, 0, 0.4)',
    zIndex: 999,
  },
  tooltipContainer: {
    position: 'absolute',
    top: '100%',
    left: 0,
    right: 0,
    marginTop: 8,
    zIndex: 1000,
  },
  tooltip: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    paddingHorizontal: 20,
    paddingVertical: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 8,
    },
    shadowOpacity: 0.25,
    shadowRadius: 16,
    elevation: 12,
  },
  tooltipText: {
    fontSize: 14,
    color: '#374151',
    lineHeight: 22,
  },
  arrow: {
    position: 'absolute',
    top: -6,
    left: 12,
    width: 0,
    height: 0,
    borderLeftWidth: 8,
    borderRightWidth: 8,
    borderBottomWidth: 8,
    borderLeftColor: 'transparent',
    borderRightColor: 'transparent',
    borderBottomColor: '#FFFFFF',
  },
});

export default InputField;
