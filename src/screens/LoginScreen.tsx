import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  TextInput,
  StyleSheet,
  Image,
  KeyboardAvoidingView,
  Platform,
  StatusBar,
  ActivityIndicator,
  Alert,
  ScrollView,
  Dimensions,
} from 'react-native';
import { StackNavigationProp } from '@react-navigation/stack';
import { RootStackParamList } from '../navigation/types';
import { useAuthStore } from '../store/authStore';
import * as api from '../services/api';
import DomainSelectionSheet from '../components/DomainSelectionSheet';
import requestSigningService from '../services/requestSigningService';
import Icon from '../components/Icon';
import InputField from '../components/InputField';
import Toast from '../components/Toast';
import { InputValidator } from '../utils/inputValidation';
import {
  responsiveWidth,
  responsiveHeight,
  responsiveFontSize,
  moderateScale,
  responsiveIconSize,
  moderateVerticalScale,
  widthPercentage,
  heightPercentage,
  SCREEN_WIDTH,
  SCREEN_HEIGHT,
} from '../utils/responsive';

type LoginScreenNavigationProp = StackNavigationProp<RootStackParamList, 'Login'>;

interface LoginScreenProps {
  navigation: LoginScreenNavigationProp;
}

interface Domain {
  id: string;
  name: string;
  displayName: string;
}

const domains: Domain[] = [
  { id: 'DIGITURK', name: 'Digiturk', displayName: 'Digiturk' },
  { id: 'DIGITURKCC', name: 'DigiturkCC', displayName: 'DigiturkCC' },
];

// Using responsive utilities instead of direct dimensions

const LoginScreen: React.FC<LoginScreenProps> = ({ navigation }) => {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [isPasswordVisible, setIsPasswordVisible] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');
  const [showToast, setShowToast] = useState(false);
  const [toastMessage, setToastMessage] = useState('');
  const [selectedDomain, setSelectedDomain] = useState<Domain>(domains[0]);
  const [showDomainSheet, setShowDomainSheet] = useState(false);
  const { login: storeLogin } = useAuthStore();

  // Add debug handler for username changes
  const handleUsernameChange = (text: string) => {
    console.log('[DEBUG] Username changing to:', text);
    setUsername(text);
  };

  // Initialize request signing service
  React.useEffect(() => {
    requestSigningService.initialize().catch(error => {
      console.warn('Failed to initialize request signing:', error);
    });
  }, []);

  const handleLogin = async () => {
    // Debug log
    console.log('[DEBUG] Login attempt - username:', username, 'type:', typeof username);
    console.log('[DEBUG] Login attempt - password length:', password?.length);
    console.log('[DEBUG] Login attempt - selectedDomain:', selectedDomain);
    
    // Enhanced username validation matching backend requirements
    const trimmedUsername = username ? username.trim() : '';
    if (!trimmedUsername || trimmedUsername.length === 0) {
      setToastMessage('Kullanıcı adı gereklidir');
      setShowToast(true);
      return;
    }

    if (trimmedUsername.length < 3 || trimmedUsername.length > 100) {
      setToastMessage('Kullanıcı adı 3-100 karakter arasında olmalıdır');
      setShowToast(true);
      return;
    }

    // Backend regex pattern: ^[a-zA-Z0-9_\-@\.\\ ]+$
    const usernamePattern = /^[a-zA-Z0-9_\-@\.\\ ]+$/;
    if (!usernamePattern.test(trimmedUsername)) {
      setToastMessage('Kullanıcı adı geçersiz karakter içeriyor');
      setShowToast(true);
      return;
    }

    // Check for SQL injection and XSS patterns
    const usernameValidation = InputValidator.validateText(trimmedUsername, 100, true);
    if (!usernameValidation.isValid) {
      setToastMessage('Kullanıcı adında güvenlik riski tespit edildi');
      setShowToast(true);
      return;
    }

    // Validate password (minimum 8 characters as per backend)
    if (!password || password.trim().length === 0) {
      setToastMessage('Şifre gereklidir');
      setShowToast(true);
      return;
    }

    if (password.length < 8) {
      setToastMessage('Şifre en az 8 karakter olmalıdır');
      setShowToast(true);
      return;
    }

    // Validate domain selection
    if (!selectedDomain || !domains.find(d => d.id === selectedDomain.id)) {
      setToastMessage('Geçerli bir domain seçiniz');
      setShowToast(true);
      return;
    }

    // Sanitize inputs (but preserve username pattern for Windows Auth)
    const sanitizedUsername = InputValidator.sanitizeInput(trimmedUsername);

    setIsLoading(true);
    setErrorMessage('');

    try {
      const response = await api.login(sanitizedUsername, password, selectedDomain.id);
      const token = response.accessToken;

      let numericUserId = response.userInfo?.id || 'unknown';
      try {
        const tokenParts = token.split('.');
        if (tokenParts.length === 3) {
          // Base64 decode that works on both iOS and Android
          const base64Decode = (str: string): string => {
            if (typeof atob !== 'undefined') {
              return atob(str);
            }
            // Fallback for Android
            const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';
            let result = '';
            str = str.replace(/=+$/, '');
            for (let i = 0; i < str.length; i += 4) {
              const encoded1 = chars.indexOf(str.charAt(i));
              const encoded2 = chars.indexOf(str.charAt(i + 1));
              const encoded3 = i + 2 < str.length ? chars.indexOf(str.charAt(i + 2)) : -1;
              const encoded4 = i + 3 < str.length ? chars.indexOf(str.charAt(i + 3)) : -1;
              const bitmap = (encoded1 << 18) | (encoded2 << 12) | (encoded3 << 6) | encoded4;
              result += String.fromCharCode((bitmap >> 16) & 255);
              if (encoded3 !== -1) result += String.fromCharCode((bitmap >> 8) & 255);
              if (encoded4 !== -1) result += String.fromCharCode(bitmap & 255);
            }
            return result;
          };
          
          const payload = JSON.parse(base64Decode(tokenParts[1]));
          if (payload.userId) {
            numericUserId = payload.userId;
          } else if (payload.sub && payload.sub !== payload.username) {
            numericUserId = payload.sub;
          }
        }
      } catch (error) {
        console.error('Error parsing JWT token:', error);
      }

      const userData = {
        id: numericUserId,
        name: response.userInfo?.name || 'Unknown User',
        email: response.userInfo?.email || username,
        avatar: 'https://i.pravatar.cc/100?u=' + username,
      };

      await storeLogin(userData, token);

      navigation.replace('MainTabs', {
        screen: 'HomeTab',
      });
    } catch (error: any) {
      console.log('Login error:', error);
      
      if (error.isNtlm) {
        // NTLM authentication error - server configuration issue
        setToastMessage('Sunucu yapılandırma hatası. Lütfen IT desteği ile iletişime geçin.');
      } else if (error.statusCode === 401) {
        setToastMessage('Kullanıcı bilgileri eşleşmedi.');
      } else if (error.statusCode === 429) {
        // Rate limit error - calculate wait time
        let waitMessage = 'Çok fazla giriş denemesi.';

        if (error.retryAfter) {
          // Convert seconds to minutes
          const minutes = Math.ceil(error.retryAfter / 60);
          waitMessage += ` ${minutes} dakika sonra tekrar deneyin.`;
        } else if (error.rateLimitReset) {
          // Calculate time until reset
          const now = Math.floor(Date.now() / 1000);
          const secondsUntilReset = error.rateLimitReset - now;
          if (secondsUntilReset > 0) {
            const minutes = Math.ceil(secondsUntilReset / 60);
            waitMessage += ` ${minutes} dakika sonra tekrar deneyin.`;
          } else {
            waitMessage += ' Lütfen daha sonra tekrar deneyin.';
          }
        } else {
          waitMessage += ' Lütfen daha sonra tekrar deneyin.';
        }

        setToastMessage(waitMessage);
      } else if (error.message) {
        // Show the actual error message from the server
        setToastMessage(error.message);
      } else {
        setToastMessage('Giriş yapılırken bir hata oluştu.');
      }
      setShowToast(true);
    } finally {
      setIsLoading(false);
    }
  };

  const togglePasswordVisibility = () => {
    setIsPasswordVisible(!isPasswordVisible);
  };

  const handleDomainSelect = (domain: Domain) => {
    setSelectedDomain(domain);
  };

  const usernameTooltipText = `Şirketiniz tarafından adınıza tanımlanan kullanıcı adını büyük harflerle giriniz.

Kullanıcı Adı Nasıl Girilir?
DT + Adınız ve Soyadınızın İlk Harfi + Soyadınız
Örnek: DTAYYILDIRIM`;

  return (
    <View style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="#F2F4F8" />
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardView}
      // keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : -100}
      >
        <ScrollView
          contentContainerStyle={styles.scrollContent}
          keyboardShouldPersistTaps="handled"
          showsVerticalScrollIndicator={false}
          bounces={false}
        >
          <View style={styles.contentWrapper}>
            {/* Main Content */}
            <View>
              {/* Header Illustration */}
              <View style={styles.illustrationContainer}>
                <Image
                  source={require('../assets/images/login-illustration.png')}
                  style={styles.illustration}
                  resizeMode="contain"
                />
              </View>

              {/* Title */}
              <Text style={styles.title}>Merhaba Digiturk'lü</Text>
              <Text style={styles.subtitle}>
                Şirketiniz tarafından adınıza tanımlanan kullanıcı adı{'\n'}
                ve şifrenizle güvenle giriş yapabilirsiniz.
              </Text>

              {/* Form Container */}
              <View style={styles.formContainer}>
                {/* Form */}
                <View style={styles.form}>
                  {/* Domain Field */}
                  <View style={styles.fieldContainer}>
                    <View style={styles.domainFieldWrapper}>
                      <Text style={styles.domainLabel}>Domain</Text>
                      <TouchableOpacity
                        style={styles.domainField}
                        onPress={() => setShowDomainSheet(true)}
                      >
                        <View style={styles.domainFieldContent}>
                          <Text style={styles.domainText}>{selectedDomain.displayName}</Text>
                          <Icon name="chevrons/Down" size={responsiveIconSize(16)} color="#6B7280" />
                        </View>
                      </TouchableOpacity>
                    </View>
                  </View>

                  {/* Username Field */}
                  <InputField
                    label="Kullanıcı Adı"
                    leftIcon="essentials/Info Light"
                    value={username}
                    onChangeText={handleUsernameChange}
                    placeholder="Size tanımlanan kullanıcı adınız"
                    autoCapitalize="characters"
                    autoCorrect={false}
                    showTooltipOnLeftIcon={true}
                    tooltipText={usernameTooltipText}
                  />


                  {/* Password Field */}
                  <View style={styles.passwordFieldContainer}>
                    <InputField
                      label="Şifre"
                      leftIcon="essentials/Info Light"
                      value={password}
                      onChangeText={setPassword}
                      placeholder="Şifrenizi giriniz"
                      secureTextEntry={!isPasswordVisible}
                      showTooltipOnLeftIcon={true}
                      tooltipText="Harf ve rakamlardan 12 haneli şifrenizi giriniz."
                    />
                    <TouchableOpacity onPress={togglePasswordVisibility} style={styles.eyeButton}>
                      <Icon
                        name={isPasswordVisible ? 'essentials/See Dark' : 'essentials/See Light'}
                        size={responsiveIconSize(20)}
                        color="#6B7280"
                      />
                    </TouchableOpacity>
                  </View>

                  {/* Login Button */}
                  <TouchableOpacity
                    style={[styles.loginButton, isLoading && styles.loginButtonDisabled]}
                    onPress={handleLogin}
                    disabled={isLoading}
                  >
                    {isLoading ? (
                      <ActivityIndicator color="#FFFFFF" size="small" />
                    ) : (
                      <Text style={styles.loginButtonText}>Giriş Yap</Text>
                    )}
                  </TouchableOpacity>
                </View>
              </View>
            </View>

            {/* Help Link - At the bottom */}
            <TouchableOpacity style={styles.helpLinkContainer}>
              <Text style={styles.helpLink}>Uygulamaya nasıl giriş yapabilirsiniz?</Text>
            </TouchableOpacity>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>

      {/* Domain Selection Sheet */}
      <DomainSelectionSheet
        visible={showDomainSheet}
        onClose={() => setShowDomainSheet(false)}
        onSelect={handleDomainSelect}
        selectedDomain={selectedDomain}
        domains={domains}
      />

      {/* Toast Notification */}
      <Toast
        visible={showToast}
        message={toastMessage}
        type="error"
        onDismiss={() => setShowToast(false)}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F2F4F8',
  },
  keyboardView: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    justifyContent: 'space-between',
    paddingHorizontal: widthPercentage(6),
    paddingTop: heightPercentage(10),
    paddingBottom: heightPercentage(4),
  },
  contentWrapper: {
    width: '100%',
    alignItems: 'center', // Center content horizontally
    flex: 1,
    justifyContent: 'space-around', // Space between main content and help link
  },
  illustrationContainer: {
    alignItems: 'center',
    marginBottom: heightPercentage(2.5),
  },
  illustration: {
    width: widthPercentage(50),
    height: heightPercentage(15),
    maxWidth: responsiveWidth(200),
    maxHeight: responsiveHeight(150),
  },
  title: {
    fontSize: responsiveFontSize(26),
    fontWeight: '700',
    color: '#7C3AED',
    textAlign: 'center',
    marginBottom: heightPercentage(1),
  },
  subtitle: {
    fontSize: responsiveFontSize(15),
    color: '#6B7280',
    textAlign: 'center',
    lineHeight: responsiveFontSize(20),
    marginBottom: heightPercentage(2.5),
  },
  formContainer: {
    backgroundColor: '#FFFFFF',
    borderRadius: moderateScale(20),
    paddingTop: heightPercentage(3),
    paddingHorizontal: widthPercentage(6),
    paddingBottom: heightPercentage(3),
    width: '100%',
  },
  form: {
    width: '100%',
  },
  fieldContainer: {
    marginBottom: heightPercentage(1.8),
  },
  passwordFieldContainer: {
    position: 'relative',
  },
  domainFieldWrapper: {
    marginBottom: heightPercentage(2),
    width: '100%',
  },
  domainLabel: {
    fontSize: responsiveFontSize(14),
    color: '#374151',
    fontWeight: '500',
    marginBottom: heightPercentage(0.8),
  },
  domainField: {
    backgroundColor: '#F9FAFB',
    borderWidth: 1,
    borderColor: '#E5E7EB',
    borderRadius: moderateScale(12),
    paddingHorizontal: responsiveWidth(16),
    height: Math.max(heightPercentage(6.5), moderateScale(52)),
    width: '100%',
  },
  domainFieldContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    flex: 1,
  },
  domainText: {
    fontSize: responsiveFontSize(16),
    color: '#1F2937',
  },
  eyeButton: {
    position: 'absolute',
    right: responsiveWidth(12),
    top: '50%',
    transform: [{ translateY: -moderateScale(12) }],
    padding: moderateScale(8),
  },
  loginButton: {
    height: Math.max(heightPercentage(6), moderateScale(52)),
    backgroundColor: '#7C3AED',
    borderRadius: moderateScale(24),
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: heightPercentage(3),
  },
  loginButtonDisabled: {
    opacity: 0.7,
  },
  loginButtonText: {
    fontSize: responsiveFontSize(16),
    fontWeight: '600',
    color: '#FFFFFF',
  },
  helpLinkContainer: {
    marginVertical: heightPercentage(1),
  },
  helpLink: {
    fontSize: responsiveFontSize(14),
    color: '#7C3AED',
    textAlign: 'center',
  },
});

export default LoginScreen;
