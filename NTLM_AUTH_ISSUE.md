# NTLM Authentication Issue - Mobile API

## Issue Summary

The mobile app is receiving an NTLM (Windows) authentication challenge when trying to access the Mobile API login endpoint, resulting in an HTML error page instead of a JSON response.

## Issue Details

- **URL Attempted**: `https://digiflowtest.digiturk.com.tr/mobile/auth/login`
- **Response Status**: 401 Unauthorized
- **Response Headers**: 
  - `content-type: text/html`
  - `www-authenticate: NTLM`
- **Expected**: JSON response with JWT tokens
- **Actual**: HTML error page requiring Windows authentication

## Root Cause

The `/mobile` path on the IIS server appears to be configured with Windows Authentication enabled, which prevents the mobile app from accessing the Mobile API endpoints. The Mobile API project has the correct code with `[AllowAnonymous]` attributes, but the requests are not reaching the application due to IIS-level authentication.

## Investigation Steps Taken

1. ✅ Verified Mobile API has proper JWT authentication configured
2. ✅ Confirmed AuthController has `[AllowAnonymous]` on login endpoint
3. ✅ Added proper User-Agent headers with "DigiHR" prefix
4. ✅ Removed all client-side security checks that could block requests
5. ✅ Added error handling for NTLM authentication responses

## Server-Side Fix Required

The issue needs to be fixed on the server side by the IT/DevOps team:

### IIS Configuration
1. Disable Windows Authentication for the `/mobile` application path
2. Enable Anonymous Authentication for the `/mobile` application path
3. Ensure the Mobile API application pool is running with proper permissions
4. Verify URL Rewrite rules are not interfering with `/mobile` requests

### Alternative Solutions
1. Deploy Mobile API to a separate subdomain without Windows Auth (e.g., `mobile-api.digiflowtest.digiturk.com.tr`)
2. Configure a reverse proxy that bypasses Windows Auth for mobile endpoints
3. Use a different port for Mobile API that doesn't have Windows Auth enabled

## Temporary Workaround

Currently, the app displays a user-friendly error message when NTLM authentication is detected:
- Turkish: "Sunucu yapılandırma hatası. Lütfen IT desteği ile iletişime geçin."
- English: "Server configuration error. Please contact IT support."

## Testing

Once the server configuration is fixed, test the following:

1. Mobile app login should work without NTLM authentication
2. API should return proper JSON responses with JWT tokens
3. Both Android and iOS should be able to authenticate successfully
4. WebView functionality should work after successful login

## Related Files

- `/src/services/api.ts` - Login API implementation with NTLM detection
- `/src/screens/LoginScreen.tsx` - UI error handling for NTLM errors
- `DigiflowAPI/src/DigiflowAPI.MobileApi/Controllers/AuthController.cs` - Server-side auth controller
- `DigiflowAPI/src/DigiflowAPI.MobileApi/Program.cs` - Mobile API configuration