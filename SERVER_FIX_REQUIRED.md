# URGENT: Server Configuration Fix Required

## Problem
Mobile app cannot login - receiving Windows Authentication (NTLM) challenge instead of JWT authentication.

## Quick Fix Steps

### 1. Open IIS Manager on digiflowtest.digiturk.com.tr

### 2. Navigate to the /mobile application

### 3. Update Authentication Settings:
- **Disable**: Windows Authentication ❌
- **Enable**: Anonymous Authentication ✅

### 4. Verify Application Pool:
- Ensure DigiflowMobileAPI pool is running
- Check identity has proper permissions

### 5. Test the Fix:
```bash
curl -X POST https://digiflowtest.digiturk.com.tr/mobile/auth/login \
  -H "Content-Type: application/json" \
  -H "User-Agent: DigiHR/1.0" \
  -d '{"Username":"test","Password":"test","DeviceId":"test","DeviceName":"test"}'
```

Expected: JSON response (even if 401)
NOT Expected: HTML with NTLM challenge

## Alternative If Above Doesn't Work

Deploy Mobile API to separate subdomain without Windows Auth:
- Example: `mobile-api.digiflowtest.digiturk.com.tr`
- Configure with JWT authentication only
- Update mobile app configuration to use new URL

## Contact
If you need the Mobile API deployment files or web.config, they should be in:
- `DigiflowAPI/src/DigiflowAPI.MobileApi/`